"""
Last update:    2024.06.01
Researcher:     MAKSYM CHERNOZHUKOV
Description:    Pipeline for Ultra-Light-Face-Analysis
Features:       Face Detection, 2D-3D Facial Landmarks, Pose Estimation
"""
import os
import cv2
import copy
import numpy as np

from icecream import ic
from pathlib import Path
from datetime import datetime
from argparse import ArgumentParser
from time import sleep, perf_counter as timer

from utils import util
from utils.constants import SD, HD, FHD
from nets.FaceDetector import FaceDetector<PERSON><PERSON><PERSON>
from nets.Landmarks3DDetectorDeep import Landmarks3DDetectorONNX as Landmarks3DDetectorONNXDEEP
from nets.Landmarks3DDetector3DDFA import Landmarks3DDetectorONNX as Landmarks3DDetectorONNX3DDFA


util.setup_multi_processes()
util.init_deterministic_seed()


def run_inference_pipeline(args, freeze=1, resolution=SD, save_fps=None, fps_limit=None, winname="ONNX Inference"):
    # Initialize VideoCapture
    cap = cv2.VideoCapture(args.source)
    
    # Set VideoCapture's properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, resolution[0])        # only for camera
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, resolution[1])       # only for camera
    cap.set(cv2.CAP_PROP_POS_MSEC, 0)                       # only for video file (or)
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)                     # only for video file (or)
        
    # Get VideoCapture's properties
    cap_fps = int(cap.get(cv2.CAP_PROP_FPS))
    cap_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    cap_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    cap_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))     # only for video file
    
    # Initialize variables
    fps_limit = fps_limit or cap_fps
    save_fps = save_fps or fps_limit
    cap_resolution = (cap_width, cap_height)
    
    # Create a window named
    cv2.namedWindow(winname, cv2.WINDOW_NORMAL)

    # Resize the window to specific width and height
    cv2.resizeWindow(winname, width=HD[0], height=HD[1])
    
    # Set the window position
    cv2.moveWindow(winname, x=FHD[0]//2-HD[0]//2, y=FHD[1]//2-HD[1]//2)
    
    # Recording
    if args.save:
        cur_date_time = datetime.now().strftime("%Y.%m.%d.%H-%M-%S")
        cur_date = ".".join(cur_date_time.split(".")[:3])

        original_path = os.path.join(args.output_dir, cur_date, "original", f"{cur_date_time}.mp4")
        modified_path = os.path.join(args.output_dir, cur_date, "modified", f"{cur_date_time}.mp4")
        
        Path(os.path.dirname(original_path)).mkdir(parents=True, exist_ok=True)
        Path(os.path.dirname(modified_path)).mkdir(parents=True, exist_ok=True)

        fourcc_MJPG = cv2.VideoWriter_fourcc(*'MJPG')   # (.avi) for motion JPEG
        fourcc_XVID = cv2.VideoWriter_fourcc(*'XVID')   # (.avi) for MPEG-4
        fourcc_mp4v = cv2.VideoWriter_fourcc(*'mp4v')   # (.mp4) for MPEG-4
        
        out_org = cv2.VideoWriter(original_path, fourcc_mp4v, save_fps, cap_resolution, isColor=True)
        out_mod = cv2.VideoWriter(modified_path, fourcc_mp4v, save_fps, resolution, isColor=True)

    # Load onnx models
    face_detector = FaceDetectorONNX(args.face_onnx,
                                     apply_all_optim=args.onnx_optimizers,
                                     device=args.device)

    if args.deep_insight:
        landmarks_detector = Landmarks3DDetectorONNXDEEP(args.landmarksDeep_onnx,
                                                         apply_all_optim=args.onnx_optimizers,
                                                         device=args.device).load_model()
    else:
        landmarks_detector = Landmarks3DDetectorONNX3DDFA(args.landmarks3DDFA_onnx,
                                                          apply_all_optim=args.onnx_optimizers,
                                                          device=args.device).load_model()
    
    count = 0
    count_skip = 0
    count_warm_up = 5
    init_time = timer()
    time_limit = timer()
    while (cap.isOpened()) and ((timer() - time_limit) < args.duration):
        count += 1
        start = timer()

        success, original = cap.read()

        if not success:
            break

        if count < count_skip:
            continue
        
        # Copy original frame
        frame = copy.copy(original)
        
        # Resize the frame
        frame = cv2.resize(frame, dsize=resolution)

        # Main process
        # =======================================================================================================
        inference_speed_start = timer()

        # Detect faces and 5 keypoints (optional)
        faces, kps = face_detector.detect(frame,
                                          input_size=(640, 640),
                                          score_thresh=args.score_threshold,
                                          nms_thresh=args.nms_threshold)
        
        # Detect 3D-landmarks and Head-Pose
        for face, kp in zip(faces, kps):
            # Extract data
            conf = face[-1]
            bbox = np.round(face[:4]).astype(np.int32)
            
            # Detect 3D landmarks and head pose
            landmark_3d_68, estimated_pose = landmarks_detector.predict(frame, bbox)
            
            # Estimate desired pose
            verification = util.estimate_desired_pose(frame, estimated_pose, verbose=True)
            
            # Draw face bbox
            x1, y1, x2, y2 = bbox
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Draw 2D 5-landmarks
            lmk = np.round(kp).astype(np.int32)
            for p in lmk:
                cv2.circle(frame, p, 3, (255, 255, 0), -1, cv2.LINE_AA)

            # Draw 3D 68-landmarks
            lmk = np.round(landmark_3d_68).astype(np.int32)
            for i, p in enumerate(lmk[:,:2]):
                # if i >= 36 and i <= 47: continue
                cv2.circle(frame, p, 1, (0, 255, 255), -1, cv2.LINE_AA)

            # Draw head pose axis
            util.draw_axis(frame, estimated_pose, verification, tdx=lmk[30][0], tdy=lmk[30][1], size=100, verbose=False)
            
        inference_speed_end = timer()
        # =======================================================================================================

        # Record video
        if args.save:
            out_org.write(original)
            out_mod.write(frame)

        # Show frame
        cv2.imshow(winname, frame)

        # WaitKey
        if cv2.waitKey(freeze) & 0xFF == ord('q'):
            break

        # Limit the inferece speed by fps_limit
        if fps_limit > 0:
            sleep_time = (1 / fps_limit) - (timer() - init_time)
            if sleep_time > 0:
                sleep(sleep_time)
            init_time = timer()

        # Caluclate inference speed
        fps = round(1 / (timer() - start))
        ms = inference_speed_end - inference_speed_start

        # Start the timer after warming up
        if count-1 == (count_warm_up+count_skip):
            start_avg = timer()
        
        # Show the Inference Speed
        print(f"FPS: {fps}, frameID: {count}")
        print(f"Inference speed FPS: {round(1 / (ms))}")
        print(f"Inference speed: {round(ms, 5)} ms")
        print("="*80)

    print(f"Average FPS: {round(1 / ((timer() - start_avg) / (count-count_warm_up-count_skip)))}")

    # Destroy all the windows
    cap.release()
    if args.save:
        out_org.release()
        out_mod.release()
    cv2.destroyAllWindows()


def main():
    video_path = os.path.join('samples', 'videos', 'SCMS.mp4')
    
    parser = ArgumentParser()
    parser.add_argument('--source', default=video_path, type=str, help='camera/video file path')
    parser.add_argument('--duration', default=float('inf'), type=float, help='running time')
    parser.add_argument('--output-dir', default="output", type=str, help='output path')
    parser.add_argument('--save', action="store_true", help='save results')
    
    parser.add_argument('--face-onnx', default="weights/SCRFD_0.5G_KPS.onnx", type=str)
    parser.add_argument('--landmarksDeep-onnx', default="weights/3D_Landmarks_68_ResNet-50_Deep.onnx", type=str)
    parser.add_argument('--landmarks3DDFA-onnx', default="weights/3D_Landmarks_68_MobileNetV1_1.0.onnx", type=str)
    parser.add_argument('--deep-insight', action="store_true", help='use landmarks-onnx')

    parser.add_argument('--nms_threshold', default=0.4, type=float, help='maximum nms threshold')
    parser.add_argument('--score_threshold', default=0.5, type=float, help='minimum conf score threshold')

    parser.add_argument('--device', default="cuda", type=str, help='cpu/cuda')
    parser.add_argument('--onnx-optimizers', default=True, type=bool, help='use all available ONNX optimizations')
    args = parser.parse_args()

    if len(args.source) == 1:
        args.source = int(args.source)
    
    run_inference_pipeline(args)


if __name__ == '__main__':
    main()
