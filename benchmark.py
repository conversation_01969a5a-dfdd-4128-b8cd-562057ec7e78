import cv2
import time
import torch
import argparse
import numpy as np
import torch.utils.data as data
import torchvision.transforms as transforms

from tqdm import tqdm
from icecream import ic

from benchmarks.DDFATestDataset import ToTensorGjz, NormalizeGjz, DDFATestDataset
from benchmarks.evaluate_aflw_aflw2000 import ana, calc_nme_alfw, calc_nme_alfw2000
from nets.Landmarks3DDetectorDeep import Landmarks3DDetectorONNX as Landmarks3DDetectorONNXDEEP
from nets.Landmarks3DDetector3DDFA import Landmarks3DDetectorONNX as Landmarks3DDetectorONNX3DDFA


def extract_param(args, root='', filelists=None, batch_size=1, num_workers=16):
    deep_insight = True if "ResNet-50_Deep" in args.onnx_path else False

    # Load Model
    if deep_insight:
        landmarks_detector = Landmarks3DDetectorONNXDEEP(args.onnx_path,
                                                         apply_all_optim=args.onnx_optimizers,
                                                         device=args.device).load_model()
    else:
        landmarks_detector = Landmarks3DDetectorONNX3DDFA(args.onnx_path,
                                                          apply_all_optim=args.onnx_optimizers,
                                                          device=args.device).load_model()
    
    # Create data loader
    dataset = DDFATestDataset(
        root=root,
        filelists=filelists,
        transform=transforms.Compose([
            ToTensorGjz(),
            NormalizeGjz(mean=127.5, std=128)
        ])
    )
    data_loader = data.DataLoader(dataset, batch_size=batch_size, num_workers=num_workers)

    outputs = []
    start = time.time()
    with torch.no_grad():
        for inputs, path in tqdm(data_loader):
            if deep_insight:
                inputs = cv2.imread(path[0], cv2.IMREAD_COLOR)

            landmark_3D_68, estimated_pose = landmarks_detector.evaluate(inputs)

            if args.verbose:
                # =================================================================
                original = cv2.imread(path[0], cv2.IMREAD_COLOR)
                lmk = np.round(landmark_3D_68).astype(np.int32)

                for p in lmk[:,:2]:
                    cv2.circle(original, p, 1, (0, 255, 255), -1, cv2.LINE_AA)

                cv2.imshow("Face", original)
                if cv2.waitKey(0) & 0xFF == ord('q'):
                    exit()
                # =================================================================

            outputs.append(landmark_3D_68.transpose()[:2, :])
        outputs = np.array(outputs, dtype=np.float32)

    print(f'Extracting params take {time.time() - start: .3f}s')
    return outputs


def benchmark_aflw(args):
    print("======== AFLW ========")
    params = extract_param(
        args=args,
        root='benchmarks/test_datasets/AFLW_GT_crop',
        filelists='benchmarks/test_datasets/AFLW_GT_crop.list')
    ana(calc_nme_alfw(params), data="aflw")


def benchmark_aflw2000(args):
    print("======== AFLW2000 ========")
    params = extract_param(
        args=args,
        root='benchmarks/test_datasets/AFLW2000-3D_crop',
        filelists='benchmarks/test_datasets/AFLW2000-3D_crop.list')
    ana(calc_nme_alfw2000(params), data="aflw2000")


def main():
    onnx_paths = [
        "weights/3D_Landmarks_68_MobileNetV1_0.5.onnx", # BGR
        "weights/3D_Landmarks_68_MobileNetV1_1.0.onnx", # BGR
        "weights/3D_Landmarks_68_ResNet-22.onnx",       # BGR
        "weights/3D_Landmarks_68_ResNet-50_Deep.onnx"]  # RGB !

    parser = argparse.ArgumentParser(description='3DDFA Benchmark')
    parser.add_argument('--onnx-path', default=onnx_paths[0], type=str)
    parser.add_argument('--device', default="cuda", type=str, help='cpu/cuda')
    parser.add_argument('--onnx-optimizers', default=True, type=bool)
    parser.add_argument('--verbose', action="store_true")
    args = parser.parse_args()

    print(args.onnx_path)

    benchmark_aflw(args)
    benchmark_aflw2000(args)


if __name__ == '__main__':
    main()
