"""
Last update:    2024.06.01
Researcher:     MAKSYM CHERNOZHUKOV
Description:    Pipeline for Ultra-Light-Face-Analysis from Deep-Insight
Features:
    - Face Detection
    - 2D 5-landmarks
    - 2D 106-landmarks
    - 3D 68-landmarks
    - Age recognition
    - Gender Recognition
    - Pose Estimation
    - Face Recognition
"""
import os
import cv2
import copy
import numpy as np

from icecream import ic
from pathlib import Path
from datetime import datetime
from argparse import ArgumentParser
from time import sleep, perf_counter as timer

from utils import util
from utils.constants import SD, HD, FHD
from insightface.app import FaceAnalysis


util.setup_multi_processes()
util.init_deterministic_seed()


def run_inference_pipeline(args, freeze=1, resolution=SD, save_fps=None, fps_limit=None, winname="ONNX Inference"):
    # Initialize VideoCapture
    cap = cv2.VideoCapture(args.source)
    
    # Set VideoCapture's properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, resolution[0])        # only for camera
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, resolution[1])       # only for camera
    cap.set(cv2.CAP_PROP_POS_MSEC, 0)                       # only for video file (or)
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)                     # only for video file (or)
        
    # Get VideoCapture's properties
    cap_fps = int(cap.get(cv2.CAP_PROP_FPS))
    cap_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    cap_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    cap_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))     # only for video file
    
    # Initialize variables
    fps_limit = fps_limit or cap_fps
    save_fps = save_fps or fps_limit
    cap_resolution = (cap_width, cap_height)
    
    # Create a window named
    cv2.namedWindow(winname, cv2.WINDOW_NORMAL)

    # Resize the window to specific width and height
    cv2.resizeWindow(winname, width=HD[0], height=HD[1])
    
    # Set the window position
    cv2.moveWindow(winname, x=FHD[0]//2-HD[0]//2, y=FHD[1]//2-HD[1]//2)
    
    # Recording
    if args.save:
        cur_date_time = datetime.now().strftime("%Y.%m.%d.%H-%M-%S")
        cur_date = ".".join(cur_date_time.split(".")[:3])

        original_path = os.path.join(args.output_dir, cur_date, "original", f"{cur_date_time}.mp4")
        modified_path = os.path.join(args.output_dir, cur_date, "modified", f"{cur_date_time}.mp4")
        
        Path(os.path.dirname(original_path)).mkdir(parents=True, exist_ok=True)
        Path(os.path.dirname(modified_path)).mkdir(parents=True, exist_ok=True)

        fourcc_MJPG = cv2.VideoWriter_fourcc(*'MJPG')   # (.avi) for motion JPEG
        fourcc_XVID = cv2.VideoWriter_fourcc(*'XVID')   # (.avi) for MPEG-4
        fourcc_mp4v = cv2.VideoWriter_fourcc(*'mp4v')   # (.mp4) for MPEG-4
        
        out_org = cv2.VideoWriter(original_path, fourcc_mp4v, save_fps, cap_resolution, isColor=True)
        out_mod = cv2.VideoWriter(modified_path, fourcc_mp4v, save_fps, resolution, isColor=True)

    # Load onnx models from insightface
    ctx_id = 0 if args.device == "cuda" else -1
    providers = ['CUDAExecutionProvider'] if args.device == "cuda" else ['CPUExecutionProvider']

    allowed_modules = ['detection', 'genderage', 'landmark_2d_106', 'landmark_3d_68', 'recognition']
    app = FaceAnalysis(providers=providers, allowed_modules=allowed_modules)
    app.prepare(ctx_id=ctx_id, det_size=(640, 640))
    
    count = 0
    count_skip = 0
    count_warm_up = 5
    init_time = timer()
    time_limit = timer()
    while (cap.isOpened()) and ((timer() - time_limit) < args.duration):
        count += 1
        start = timer()

        success, original = cap.read()

        if not success:
            break

        if count < count_skip:
            continue
        
        # Copy original frame
        frame = copy.copy(original)
        
        # Resize the frame
        frame = cv2.resize(frame, dsize=resolution)

        # Main process
        # =======================================================================================================
        inference_speed_start = timer()

        # ["bbox", "kps", "det_score", "gender", "age", "landmark_2d_106", "landmark_3d_68", "pose", "embedding"]
        faces = app.get(frame)

        for face in faces:
            # Face detection bbox
            x1, y1, x2, y2 = np.round(face.bbox).astype(np.int32)
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # Face detection score
            cv2.putText(frame, f"{face.det_score:.2}", (x1, y2), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

            # 2D 5-landmarks
            kps = np.round(face.kps).astype(np.int32)
            for p in kps:
                cv2.circle(frame, p, 1, (0, 255, 0), 1, cv2.LINE_AA)

            # Gender, and age recognition
            cv2.putText(frame, f"{face.sex}:{face.age}", (x1, y1), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

            # 2D 106-landmarks
            lmk = np.round(face.landmark_2d_106).astype(np.int32)
            for p in lmk:
                cv2.circle(frame, p, 1, (0, 255, 0), 1, cv2.LINE_AA)

            # 3D 68-landmarks
            lmk = np.round(face.landmark_3d_68).astype(np.int32)
            for p in lmk[:,:2]:
                cv2.circle(frame, p, 1, (0, 255, 255), 1, cv2.LINE_AA)
            
            # Face pose
            verification = True
            pitch, yaw, roll = face.pose
            estimated_pose = {"yaw": yaw, "pitch": pitch, "roll": roll}
            util.draw_axis(frame, estimated_pose, verification, tdx=lmk[30][0], tdy=lmk[30][1], size=100, verbose=False)

        inference_speed_end = timer()
        # =======================================================================================================

        # Record video
        if args.save:
            out_org.write(original)
            out_mod.write(frame)

        # Show frame
        cv2.imshow(winname, frame)

        # WaitKey
        if cv2.waitKey(freeze) & 0xFF == ord('q'):
            break

        # Limit the inferece speed by fps_limit
        if fps_limit > 0:
            sleep_time = (1 / fps_limit) - (timer() - init_time)
            if sleep_time > 0:
                sleep(sleep_time)
            init_time = timer()

        # Caluclate inference speed
        fps = round(1 / (timer() - start))
        ms = inference_speed_end - inference_speed_start

        # Start the timer after warming up
        if count-1 == (count_warm_up+count_skip):
            start_avg = timer()
        
        # Show the Inference Speed
        print(f"FPS: {fps}, frameID: {count}")
        print(f"Inference speed FPS: {round(1 / (ms))}")
        print(f"Inference speed: {round(ms, 5)} ms")
        print("="*80)

    print(f"Average FPS: {round(1 / ((timer() - start_avg) / (count-count_warm_up-count_skip)))}")

    # Destroy all the windows
    cap.release()
    if args.save:
        out_org.release()
        out_mod.release()
    cv2.destroyAllWindows()


def main():
    video_path = os.path.join('samples', 'videos', 'SCMS.mp4')
    
    parser = ArgumentParser()
    parser.add_argument('--source', default=video_path, type=str, help='camera/video file path')
    parser.add_argument('--duration', default=float('inf'), type=float, help='running time')
    parser.add_argument('--output-dir', default="output", type=str, help='output path')
    parser.add_argument('--save', action="store_true", help='save results')
    parser.add_argument('--device', default="cuda", type=str, help='cpu/cuda')
    args = parser.parse_args()

    if len(args.source) == 1:
        args.source = int(args.source)
    
    run_inference_pipeline(args)


if __name__ == '__main__':
    main()
