# Ultra-Light-Face-Analysis

## Introduction
#### Related papers
- [Deep-Insight: SCRFD](https://arxiv.org/abs/2105.04714)
- [Deep-Insight: 3D Facial Landmarks](https://arxiv.org/abs/1812.01936)
- [3DDFAv1: 3D Facial Landmarks](https://arxiv.org/abs/1804.01005)
- [3DDFAv2: 3D Facial Landmarks](https://arxiv.org/abs/2009.09960)

![SCRFD performance](./assets/performance-comparison/SCRFD.png "SCRFD performance")
![3D-68 Facial Landmarks performance](./assets/performance-comparison/3D-MobileNets.png "3D-68 Facial Landmarks performance")

## SCRFD Face Detection Models

|      Name      | Easy  | Medium | Hard  | FLOPs(G) | Params(M) | Infer(ms) | Infer(FPS) |
| :------------: | ----- | ------ | ----- | -------- | --------- | --------- | ---------- |
| SCRFD_0.5G     | 90.57 | 88.12  | 68.51 | 0.5      | 0.57      | 8.3       | 120        |
| SCRFD_1.0G     | 92.38 | 90.57  | 74.80 | 1.0      | 0.64      | 14.2      | 70         |
| SCRFD_2.5G     | 93.78 | 92.16  | 77.87 | 2.5      | 0.67      | 22.7      | 44         |
| SCRFD_10G      | 95.16 | 93.87  | 83.05 | 10       | 3.86      | 69.5      | 14         |
| SCRFD_34G      | 96.06 | 94.92  | 85.29 | 34       | 9.80      | 228.1     | 4.4        |
| SCRFD_0.5G_KPS | 90.97 | 88.44  | 69.49 | 0.5      | 0.57      | 7.5       | 131        |
| SCRFD_2.5G_KPS | 93.80 | 92.02  | 77.13 | 2.5      | 0.82      | 22.5      | 44         |
| SCRFD_10G_KPS  | 95.40 | 94.01  | 82.80 | 10       | 4.23      | 70.9      | 14         |

## 2D/3D Face Alignment models Deep-Insight

|      Name                             | Type  | Points | Backbone      | MACs(M) | FLOPs(M) | Params(M) | Infer(ms) | Infer(FPS) |
| :------------------------------------:| ----- | ------ | ------------- | ------- | -------- | --------- | --------- | ---------- |
| 2D_Landmarks_106_MobileNetV1_0.5.onnx | 2D    | 106    | MobileNet-0.5 | 0       | 0        | 0         | 0         | 0          |
| 3D_Landmarks_68_ResNet-50_Deep.onnx   | 3D    | 68     | ResNet-50     | 3071.5  | 3053.0   | 35.809    | 14.5      | 69         |

## 2D/3D Face Alignment models 3DDFA

|      Name                            | Type  | Points | Backbone         | MACs(M) | FLOPs(M) | Params(M) | Infer(ms) | Infer(FPS) |
| :----------------------------------: | ----- | ------ | ---------------- | ------- | -------- | --------- | --------- | ---------- |
| 3D_Landmarks_68_MobileNetV1_0.5.onnx | 3D    | 68     | MobileNet-V1-0.5 | 48.7    | 46.5     | 0.844     | 0.5       | 1895       |
| 3D_Landmarks_68_MobileNetV1_1.0.onnx | 3D    | 68     | MobileNet-V1-1.0 | 181.9   | 177.4    | 3.260     | 1.0       | 955        |
| 3D_Landmarks_68_ResNet-22.onnx       | 3D    | 68     | ResNet-22        | 2661.2  | 2656.0   | 18.442    | 10.0      | 100        |


## 2D/3D Face Alignment models performance
|      Name                            | Dataset  | AoV     | NME (mean/std) |
| :----------------------------------: | -------- | ------- | -------------- |
| 3D_Landmarks_68_MobileNetV1_0.5.onnx | AFLW     | [0, 90] | 4.738 / 0.463  |
| 3D_Landmarks_68_MobileNetV1_1.0.onnx | AFLW     | [0, 90] | 4.600 / 0.402  |
| 3D_Landmarks_68_ResNet-22.onnx       | AFLW     | [0, 90] | 4.395 / 0.409  |
| 3D_Landmarks_68_ResNet-50_Deep.onnx  | AFLW     | [0, 90] | 4.405 / 0.645  |
| 3D_Landmarks_68_MobileNetV1_0.5.onnx | AFLW2000 | [0, 90] | 3.798 / 0.766  |
| 3D_Landmarks_68_MobileNetV1_1.0.onnx | AFLW2000 | [0, 90] | 3.682 / 0.721  |
| 3D_Landmarks_68_ResNet-22.onnx       | AFLW2000 | [0, 90] | 3.670 / 0.869  |
| 3D_Landmarks_68_ResNet-50_Deep.onnx  | AFLW2000 | [0, 90] | 3.935 / 0.958  |

#### Notes

* ``_KPS``: means this model includes 5 facial keypoints.
* ``Infer(ms/FPS)*`` was evaluated on my laptop's CPU (i7-11800H) with default configs.
* All metrics were evaluated at a resolution (640x480) in a cycle of 1_000.
* ``SCRFD`` static, no simplify
* ``Landmarks3DDetector3DDFA`` static, simplify
* ``Landmarks3DDetectorDeep`` batch dinamic, simplify
* ``3DDFA`` was trained on ``BGR`` data.
* ``Deep-Insight`` was trained on ``RGB`` data (``swapRB=True``).
* More comprehensive analyze for model performance at ``benchmarks/note.txt``
* This repo supports ONNX inference only, see reference for more details.

## Project Tree
```
Ultra-Light-Face-Analysis
├── assets
├── benchmarks
├── architectures
|   ├── mobilenet_v1.py
|   ├── mobilenet_v3.py
|   └── resnet22.py
├── nets
│   ├── FaceDetector.py
│   ├── Landmarks3DDetector3DDFA.py
│   └── Landmarks3DDetectorDeep.py
├── samples
│   ├── images
│   │   └── faces.jpeg
│   └── videos
│       └── SCMS.mp4
├── utils
│   ├── constants.py
│   ├── inference_speed_estimation.py
│   ├── ONNX_EVALUATION.py
│   └── util.py
├── weights
|   ├── pkl_references
|   │   ├── bfm_noneck_v3.pkl
|   │   ├── bfm_noneck_v3_slim.pkl
|   │   ├── meanshape_68_deep.pkl
|   │   └── param_mean_std_62d_120x120.pkl
|   ├── 3D_Landmarks_68_MobileNetV1_0.5.onnx
|   ├── 3D_Landmarks_68_MobileNetV1_1.0.onnx
|   ├── 3D_Landmarks_68_ResNet-22.onnx
|   ├── 3D_Landmarks_68_ResNet-50_Deep.onnx
│   ├── SCRFD_0.5G.onnx
│   ├── SCRFD_1.0G.onnx
│   ├── SCRFD_2.5G.onnx
│   ├── SCRFD_10G.onnx
│   ├── SCRFD_34G.onnx
│   ├── SCRFD_0.5G_KPS.onnx
│   ├── SCRFD_2.5G_KPS.onnx
│   └── SCRFD_10G_KPS.onnx
├── benchmark.py
├── demo_insightface_image.py
├── demo_insightface_video.py
├── demo.py
└── README.md
```

## Create Ultra-Light-Face-Analysis conda environment
```bash
$ conda create --name ULFA python=3.11 -y
```
```bash
$ pip install torch --index-url https://download.pytorch.org/whl/cu118
```
```bash
$ pip install opencv-python onnxruntime-gpu scikit-image tqdm icecream insightface
```
OR
```bash
# There are some issue with onnxruntime vs CUDA compatibility
$ pip install opencv-python torch torchvision onnx onnxruntime-gpu onnx-simplifier onnxoptimizer tqdm icecream insightface
```

## Run demo
```bash
$ python demo.py --source [VIDEO_PATH] --device [CPU/CUDA] --save --deep-insight
```

## Run InsightFace demo
```bash
$ python demo_insightface_image.py --source [IMAGE_PATH] --output-dir [OUTPUT_PATH] --device [CPU/CUDA] --save
$ python demo_insightface_video.py --source [VIDEO_PATH] --output-dir [OUTPUT_PATH] --device [CPU/CUDA] --save
```

## Run benchmark
```bash
$ python benchmark.py --onnx-path [ONNX_PATH] --device [CPU/CUDA] --verbose
```

### Demo Results:
![Head Pose Example](./assets/head-poses/head-pose-v0.png "Head Pose Example")
![3D-68 Facial Landmarks Gif Example](./assets/gifs/3D-landmarks.gif "3D-68 Facial Landmarks Gif Example")
![3D-68 Facial Landmarks Example](./assets/3D-landmarks/3D-v2.png "3D-68 Facial Landmarks Example")

## TODO list:

- [ ] Modify ReadMe
- [ ] Find Samples
- [ ] Record short web demo
- [ ] 3D Face Reconstruction

## Reference

* [3DDFA](https://github.com/cleardusk/3DDFA)
* [3DDFA_V2](https://github.com/cleardusk/3DDFA_V2/tree/master)
* [DeepInsight](https://github.com/deepinsight/insightface/tree/master)
