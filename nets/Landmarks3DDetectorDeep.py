import cv2
import torch
import onnxruntime
import numpy as np

from utils import util
from icecream import ic
from skimage import transform as trans


def transform(data, center, output_size, scale, rotation=0):
    rot = float(rotation) * np.pi / 180.0
    t1 = trans.SimilarityTransform(scale=scale)
    cx = center[0] * scale
    cy = center[1] * scale
    t2 = trans.SimilarityTransform(translation=(-1 * cx, -1 * cy))
    t3 = trans.SimilarityTransform(rotation=rot)
    t4 = trans.SimilarityTransform(translation=(output_size / 2,
                                                output_size / 2))
    t = t1 + t2 + t3 + t4
    M = t.params[0:2]
    cropped = cv2.warpAffine(data, M, (output_size, output_size), borderValue=0.0)
    return cropped, M


def trans_points3d(pts, M):
    scale = np.sqrt((M[0][0] * M[0][0]) + (M[0][1] * M[0][1]))
    new_pts = np.zeros(shape=pts.shape, dtype=np.float32)
    for i in range(pts.shape[0]):
        pt = pts[i]
        new_pt = np.array([pt[0], pt[1], 1.0], dtype=np.float32)
        new_pt = np.dot(M, new_pt)
        new_pts[i][0:2] = new_pt[0:2]
        new_pts[i][2] = pt[2] * scale
    return new_pts


def estimate_affine_matrix_3d23d(X, Y):
    ''' Using least-squares solution 
    Args:
        X: [n, 3]. 3D points(fixed)
        Y: [n, 3]. corresponding 3D points(moving). Y = PX
    Returns:
        P_Affine: (3, 4). Affine camera matrix (the 3rd row is [0, 0, 0, 1]).
    '''
    X_homo = np.hstack((X, np.ones([X.shape[0], 1])))	# n x 4
    P = np.linalg.lstsq(X_homo, Y, rcond=-1)[0].T		# Affine matrix. 3 x 4
    return P


def P2sRt(P):
    ''' Decomposition Affine Camera Matrix

    Args: 
        P: Affine Camera Matrix (3, 4) transforms 3D coordinates into 2D coordinates.
        
    Returns:
        s: Scale factor.
        R: Rotation matrix. (3, 3).
        t: Translation. (3,).
    '''
    R1 = P[0:1, :3]
    R2 = P[1:2, :3]

    # Euclidean Norm
    N1 = np.linalg.norm(R1)
    N2 = np.linalg.norm(R2)    

    # Calculate translation
    t = P[:, 3]

    # Calculate scale factor
    s = (N1 + N2) / 2.0

    # Calculate Rotation Matrix
    r1 = R1 / N1
    r2 = R2 / N2
    r3 = np.cross(r1, r2)

    R = np.concatenate((r1, r2, r3), axis=0)

    return s, R, t


def matrix2angle(R):
    """ Converts a rotation matrix to Euler angles (yaw, pitch, roll) in degrees.

    Args:
        R: A 3x3 rotation matrix.

    Returns:
        estimated_pose (dict): Euler angles (yaw, pitch, roll) in degrees.
    """
    one = 1 - np.finfo(float).eps   # ≈ 1

    # Check if pitch angle is close to ±90 degrees
    singular = np.abs(R[2, 0]) >= one
    
    if not singular:
        x = np.arcsin(R[2, 0])
        y = np.arctan2(R[2, 1], R[2, 2])
        z = np.arctan2(R[1, 0], R[0, 0])
    else:
        x = np.arcsin(np.sign(R[2, 0]))
        y = np.arctan2(R[0, 1], R[0, 0])
        z = 0

    estimated_pose = {
        'yaw': -np.rad2deg(x), 'pitch': np.rad2deg(y), 'roll': np.rad2deg(z)
    }
    return estimated_pose


class Landmarks3DDetectorONNX:
    def __init__(self, onnx_path=None, apply_all_optim=True, device="cpu") -> None:
        self.lmk_dim = 3
        self.lmk_num = 68
        self.input_std = 1.0
        self.input_mean = 0.0
        self.require_pose = True

        self.scalefactor = 1.0 / self.input_std
        self.mean = (self.input_mean, self.input_mean, self.input_mean)

        self.device = device
        self.onnx_path = onnx_path
        self.apply_all_optim = apply_all_optim

        self.meanshape_lmk_3D_68 = util.meanshape_lmk_3D_68

    def load_model(self):
        # Set the provider for ONNX inference
        providers = ['CUDAExecutionProvider'] if self.device == "cuda" else ['CPUExecutionProvider']

        # Set graph optimization level
        sess_options = onnxruntime.SessionOptions()
        if self.apply_all_optim:
            sess_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_ALL
        else:
            sess_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_EXTENDED
        
        # Create an inference session with the ONNX model
        self.session = onnxruntime.InferenceSession(self.onnx_path, sess_options=sess_options, providers=providers)

        # Get inputs, outputs information for inference
        self.input_name = self.session.get_inputs()[0].name					# "data"
        self.input_size = self.session.get_inputs()[0].shape[-1]			# (None, 3, 192, 192) --> 192
        self.input_shape = tuple(self.session.get_inputs()[0].shape[-2:])	# (192, 192)
        self.output_names = self.session.get_outputs()[0].name				# ['fc1']
        return self

    def predict(self, img, bbox, zoom_out=1.5):
        # Calculate the center, width, height of the face ROI
        center = (bbox[2] + bbox[0]) / 2, (bbox[3] + bbox[1]) / 2
        width, height = (bbox[2] - bbox[0]), (bbox[3] - bbox[1])

        # Calculate the zoom_out scale ratio
        _scale = self.input_size / (max(width, height) * zoom_out)
        
        # Zoom-out the cropped face
        aimg, M = transform(img, center, self.input_size, _scale)

        # Pre-process the input
        blob = cv2.dnn.blobFromImage(aimg, self.scalefactor, self.input_shape, self.mean, swapRB=True)

        # Get prediction
        pred = self.session.run([self.output_names], {self.input_name : blob})[0][0]

        # Reshape the prediction
        pred = pred.reshape((-1, 3))	# (3309,) --> (1103, 3)
        pred = pred[-self.lmk_num:, :]	# (1103, 3) --> (68, 3)

        # Transform the predicted coordinates from a normalized space
        pred[:, 0:2] += 1
        pred[:, :] *= (self.input_size // 2)

        # Calculate invertAffineMatrix
        IM = cv2.invertAffineTransform(M)

        # Transform the 3D landmarks
        landmark_3D_68 = trans_points3d(pred, IM)

        # Calculate yaw, pitch, roll
        estimated_pose = None
        if self.require_pose:
            P = estimate_affine_matrix_3d23d(self.meanshape_lmk_3D_68, landmark_3D_68)
            s, R, t = P2sRt(P)
            estimated_pose = matrix2angle(R)
            
        return landmark_3D_68, estimated_pose

    def evaluate(self, img):
        # Calculate the center, width, height of the face ROI
        size = max(img.shape)
        center = size / 2, size / 2

        # Calculate the zoom_out scale ratio
        _scale = 1.0
        
        # Zoom-out the cropped face
        aimg, M = transform(img, center, self.input_size, _scale)

        # Pre-process the input
        blob = cv2.dnn.blobFromImage(aimg, self.scalefactor, self.input_shape, self.mean, swapRB=True)

        # Get prediction
        pred = self.session.run([self.output_names], {self.input_name : blob})[0][0]

        # Reshape the prediction
        pred = pred.reshape((-1, 3))	# (3309,) --> (1103, 3)
        pred = pred[-self.lmk_num:, :]	# (1103, 3) --> (68, 3)

        # Transform the predicted coordinates from a normalized space
        pred[:, 0:2] += 1
        pred[:, :] *= (self.input_size // 2)

        # Calculate invertAffineMatrix
        IM = cv2.invertAffineTransform(M)

        # Transform the 3D landmarks
        landmark_3D_68 = trans_points3d(pred, IM)

        # Calculate yaw, pitch, roll
        estimated_pose = None
        if self.require_pose:
            P = estimate_affine_matrix_3d23d(self.meanshape_lmk_3D_68, landmark_3D_68)
            s, R, t = P2sRt(P)
            estimated_pose = matrix2angle(R)
            
        return landmark_3D_68, estimated_pose
