import cv2
import torch
import pickle
import onnxruntime
import numpy as np

from utils import util
from icecream import ic


def base_info_BFMModel(bfm, shape_dim=40, exp_dim=10):
    u = bfm.get('u').astype(np.float32)
    w_shp = bfm.get('w_shp').astype(np.float32)[..., :shape_dim]
    w_exp = bfm.get('w_exp').astype(np.float32)[..., :exp_dim]
    keypoints = bfm.get('keypoints').astype(np.int32)

    u_base = u[keypoints].reshape(-1, 1)
    w_shp_base = w_shp[keypoints]
    w_exp_base = w_exp[keypoints]
    return u_base, w_shp_base, w_exp_base


def zoom_out_face(bbox):
    # Zoom-out the cropped face
    x1, y1, x2, y2 = bbox
    w = (x2 - x1)
    h = (y2 - y1)

    old_size = (w + h) / 2.0
    center_x = x1 + (w / 2.0)
    center_y = y1 + (h / 2.0) + (old_size * 0.14)
    size = int(old_size * 1.58)

    x1 = center_x - (size / 2.0)
    y1 = center_y - (size / 2.0)
    x2 = x1 + size
    y2 = y1 + size
    return [x1, y1, x2, y2]


def crop_img(img, roi_box):
    h, w = img.shape[:2]

    sx, sy, ex, ey = [int(round(_)) for _ in roi_box]
    dh, dw = ey - sy, ex - sx
    if len(img.shape) == 3:
        res = np.zeros((dh, dw, 3), dtype=np.uint8)
    else:
        res = np.zeros((dh, dw), dtype=np.uint8)

    if sx < 0:
        sx, dsx = 0, -sx
    else:
        dsx = 0

    if ex > w:
        ex, dex = w, dw - (ex - w)
    else:
        dex = dw

    if sy < 0:
        sy, dsy = 0, -sy
    else:
        dsy = 0

    if ey > h:
        ey, dey = h, dh - (ey - h)
    else:
        dey = dh

    res[dsy:dey, dsx:dex] = img[sy:ey, sx:ex]
    return res


def _parse_param(param):
    """ matrix pose form """

    # pre-defined templates for parameter
    trans_dim, shape_dim, exp_dim = 12, 40, 10

    R_ = param[:trans_dim].reshape(3, -1)
    R = R_[:, :3]
    offset = R_[:, -1].reshape(3, 1)
    alpha_shp = param[trans_dim:trans_dim + shape_dim].reshape(-1, 1)
    alpha_exp = param[trans_dim + shape_dim:].reshape(-1, 1)
    return R, offset, alpha_shp, alpha_exp


def match_with_face(pts3d, bbox, size):
    pts3d[0, :] -= 1
    pts3d[2, :] -= 1
    pts3d[1, :] = size - pts3d[1, :]

    sx, sy, ex, ey = bbox
    scale_x = (ex - sx) / size
    scale_y = (ey - sy) / size
    pts3d[0, :] = pts3d[0, :] * scale_x + sx
    pts3d[1, :] = pts3d[1, :] * scale_y + sy
    
    pts3d[2, :] *= (scale_x + scale_y) / 2.0
    pts3d[2, :] -= np.min(pts3d[2, :])
    return np.array(pts3d, dtype=np.float32)


def P2sRt(P):
    ''' Decomposition Affine Camera Matrix

    Args: 
        P: Affine Camera Matrix (3, 4) transforms 3D coordinates into 2D coordinates.
        
    Returns:
        s: Scale factor.
        R: Rotation matrix. (3, 3).
        t: Translation. (3,).
    '''
    R1 = P[0:1, :3]
    R2 = P[1:2, :3]

    # Euclidean Norm
    N1 = np.linalg.norm(R1)
    N2 = np.linalg.norm(R2)    

    # Calculate translation
    t = P[:, 3]

    # Calculate scale factor
    s = (N1 + N2) / 2.0

    # Calculate Rotation Matrix
    r1 = R1 / N1
    r2 = R2 / N2
    r3 = np.cross(r1, r2)

    R = np.concatenate((r1, r2, r3), axis=0)

    return s, R, t


def matrix2angle(R):
    """ Converts a rotation matrix to Euler angles (yaw, pitch, roll) in degrees.

    Args:
        R: A 3x3 rotation matrix.

    Returns:
        estimated_pose (dict): Euler angles (yaw, pitch, roll) in degrees.
    """
    one = 1 - np.finfo(float).eps   # ≈ 1

    # Check if pitch angle is close to ±90 degrees
    singular = np.abs(R[2, 0]) >= one
    
    if not singular:
        x = np.arcsin(R[2, 0])
        y = np.arctan2(R[2, 1], R[2, 2])
        z = np.arctan2(R[1, 0], R[0, 0])
    else:
        x = np.arcsin(np.sign(R[2, 0]))
        y = np.arctan2(R[0, 1], R[0, 0])
        z = 0

    estimated_pose = {
        'yaw': -np.rad2deg(x), 'pitch': -np.rad2deg(y), 'roll': -np.rad2deg(z)
    }
    return estimated_pose


class Landmarks3DDetectorONNX:
    def __init__(self, onnx_path=None, apply_all_optim=True, device="cpu") -> None:
        self.require_pose = True
        
        self.device = device
        self.onnx_path = onnx_path
        self.apply_all_optim = apply_all_optim

        self.bfm = util.bfm
        self.param_mean_std = util.param_mean_std
        
    def load_model(self):
        # Set the provider for ONNX inference
        providers = ['CUDAExecutionProvider'] if self.device == "cuda" else ['CPUExecutionProvider']

        # Set graph optimization level
        sess_options = onnxruntime.SessionOptions()
        if self.apply_all_optim:
            sess_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_ALL
        else:
            sess_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_EXTENDED

        # Create an inference session with the 3D_Landmarks and BFM ONNX models
        self.session = onnxruntime.InferenceSession(self.onnx_path, sess_options=sess_options, providers=providers)

        # Get inputs, outputs information for inference
        self.input_name = self.session.get_inputs()[0].name					# "input"
        self.input_size = self.session.get_inputs()[0].shape[-1]			# [1, 3, 120, 120] --> 120
        self.input_shape = tuple(self.session.get_inputs()[0].shape[-2:])	# (120, 120)
        self.output_names = self.session.get_outputs()[0].name				# 'output'

        # Load BFM optimization
        self.u, self.w_shp, self.w_exp = base_info_BFMModel(self.bfm, shape_dim=40, exp_dim=10)

        # Params normalization config
        self.param_std = self.param_mean_std.get('std')
        self.param_mean = self.param_mean_std.get('mean')

        return self

    def predict(self, img_original, bbox):
        # Zoom-out the face ROI
        bbox = zoom_out_face(bbox)

        # Crop the face with a margin
        img = crop_img(img_original, bbox)

        # Pre-process the input
        img = cv2.resize(img, dsize=self.input_shape, interpolation=cv2.INTER_LINEAR)
        img = img.astype(np.float32).transpose(2, 0, 1)[np.newaxis, ...]
        img = (img - 127.5) / 128.0

        # Get prediction
        param = self.session.run([self.output_names], {self.input_name: img})[0]

        # Post-process the output
        param = param.flatten().astype(np.float32)
        param = (param * self.param_std) + self.param_mean

        # Convert from (62,) params --> (68,3) landmarks
        R, offset, alpha_shp, alpha_exp = _parse_param(param)
        pts3d = R @ (self.u + self.w_shp @ alpha_shp + self.w_exp @ alpha_exp).reshape(3, -1, order='F') + offset
        
        # Shift 3D landmarks to the cropped face
        landmark_3D_68 = match_with_face(pts3d, bbox, self.input_size).transpose()

        # Calculate yaw, pitch, roll
        estimated_pose = None
        if self.require_pose:
            P = param[:12].reshape(3, -1)
            s, R, t = P2sRt(P)
            estimated_pose = matrix2angle(R)
            
        return landmark_3D_68, estimated_pose

    def evaluate(self, img):
        # Get prediction
        param = self.session.run([self.output_names], {self.input_name : img.numpy()})[0]

        # Post-process the output
        param = param.flatten().astype(np.float32)
        param = (param * self.param_std) + self.param_mean

        # Convert from (62,) params --> (68,3) landmarks
        R, offset, alpha_shp, alpha_exp = _parse_param(param)
        pts3d = R @ (self.u + self.w_shp @ alpha_shp + self.w_exp @ alpha_exp).reshape(3, -1, order='F') + offset
        
        # Shift 3D landmarks to the cropped face
        bbox = [0, 0, 120, 120]
        landmark_3D_68 = match_with_face(pts3d, bbox, self.input_size).transpose()

        # Calculate yaw, pitch, roll
        estimated_pose = None
        if self.require_pose:
            P = param[:12].reshape(3, -1)
            s, R, t = P2sRt(P)
            estimated_pose = matrix2angle(R)
            
        return landmark_3D_68, estimated_pose
