"""
Last update:    2024.06.01
Researcher:     MAKSYM CHERNOZHUKOV
Description:    Pipeline for Ultra-Light-Face-Analysis from Deep-Insight
Features:
    - Face Detection
    - 2D 5-landmarks
    - 2D 106-landmarks
    - 3D 68-landmarks
    - Age recognition
    - Gender Recognition
    - Pose Estimation
    - Face Recognition
"""
import os
import cv2
import torch
import numpy as np

from argparse import ArgumentParser

from utils import util
from utils.constants import SD, HD, FHD
from insightface.app import FaceAnalysis


util.setup_multi_processes()
util.init_deterministic_seed()


def run_inference_pipeline(args, window_name="ONNX Inference"):
    # Load the model to detect a face and genderage
    ctx_id = 0 if args.device == "cuda" else -1
    providers = ['CUDAExecutionProvider'] if args.device == "cuda" else ['CPUExecutionProvider']

    allowed_modules = ['detection', 'genderage', 'landmark_2d_106', 'landmark_3d_68', 'recognition']
    app = FaceAnalysis(providers=providers, allowed_modules=allowed_modules)
    app.prepare(ctx_id=ctx_id, det_size=(640, 640))

    # Create a window named 'Frame'
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)

    # Resize the window to specific width and height
    cv2.resizeWindow(window_name, width=HD[0], height=HD[1])

    # Load image
    image = cv2.imread(args.source)

    # Main process
    # =======================================================================================================
    faces = app.get(image)

    for face in faces:
        # Face detection bbox
        x1, y1, x2, y2 = np.round(face.bbox).astype(np.int32)
        cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)

        # Face detection score
        cv2.putText(image, f"{face.det_score:.2}", (x1, y2), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

        # 2D 5-landmarks
        kps = np.round(face.kps).astype(np.int32)
        for p in kps:
            cv2.circle(image, p, 1, (0, 255, 0), 1, cv2.LINE_AA)

        # Gender, and age recognition
        cv2.putText(image, f"{face.sex}:{face.age}", (x1, y1), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        # 2D 106-landmarks
        lmk = np.round(face.landmark_2d_106).astype(np.int32)
        for p in lmk:
            cv2.circle(image, p, 1, (0, 255, 0), 1, cv2.LINE_AA)

        # 3D 68-landmarks
        lmk = np.round(face.landmark_3d_68).astype(np.int32)
        for p in lmk[:,:2]:
            cv2.circle(image, p, 1, (0, 255, 255), 1, cv2.LINE_AA)
        
        # Face pose
        verification = True
        pitch, yaw, roll = face.pose
        estimated_pose = {"yaw": yaw, "pitch": pitch, "roll": roll}
        util.draw_axis(image, estimated_pose, verification, tdx=lmk[30][0], tdy=lmk[30][1], size=100, verbose=False)
    # =======================================================================================================

    if args.save:
        # Create output folder
        os.makedirs(args.output_dir, exist_ok=True)
        
        # Save results
        output_path = os.path.join(args.output_dir, os.path.basename(args.source))
        cv2.imwrite(output_path, image)

    # Show image
    cv2.imshow(window_name, image)

    # WaitKey
    cv2.waitKey(0)
    cv2.destroyAllWindows()


def main():
    image_path = os.path.join('samples', 'images', 'faces.jpeg')
    
    parser = ArgumentParser()
    parser.add_argument('--source', default=image_path, type=str, help='image file path')
    parser.add_argument('--output-dir', default="output", type=str, help='output path')
    parser.add_argument('--save', action="store_true", help='save results')
    parser.add_argument('--device', default="cuda", type=str, help='cpu/cuda')
    args = parser.parse_args()

    run_inference_pipeline(args)


if __name__ == '__main__':
    main()
