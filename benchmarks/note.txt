""" 
Metric: NME

3D_Landmarks_68_MobileNetV1_0.5.onnx
    AFLW
    100%|█████████████████████████████████████████████████████████████████████████████████████████| 21080/21080 [00:22<00:00, 955.63it/s]
    Extracting params take  22.078s
    [ 0, 30]        Mean: 4.234, Std: 4.072
    [30, 60]        Mean: 4.627, Std: 3.989
    [60, 90]        Mean: 5.352, Std: 6.347
    [ 0, 90]        Mean: 4.738, Std: 0.463

    AFLW2000
    100%|███████████████████████████████████████████████████████████████████████████████████████████| 2000/2000 [00:02<00:00, 864.69it/s]
    Extracting params take  2.316s
    [ 0, 30]        Mean: 2.927, Std: 1.447
    [30, 60]        Mean: 3.674, Std: 1.760
    [60, 90]        Mean: 4.792, Std: 2.520
    [ 0, 90]        Mean: 3.798, Std: 0.766


3D_Landmarks_68_MobileNetV1_1.0.onnx
    AFLW
    100%|█████████████████████████████████████████████████████████████████████████████████████████| 21080/21080 [00:25<00:00, 820.80it/s]
    Extracting params take  25.702s
    [ 0, 30]        Mean: 4.166, Std: 4.216
    [30, 60]        Mean: 4.500, Std: 3.847
    [60, 90]        Mean: 5.134, Std: 6.249
    [ 0, 90]        Mean: 4.600, Std: 0.402

    AFLW2000
    100%|███████████████████████████████████████████████████████████████████████████████████████████| 2000/2000 [00:02<00:00, 767.91it/s]
    Extracting params take  2.608s
    [ 0, 30]        Mean: 2.839, Std: 1.319
    [30, 60]        Mean: 3.608, Std: 1.768
    [60, 90]        Mean: 4.601, Std: 2.054
    [ 0, 90]        Mean: 3.682, Std: 0.721


snapshots/snapshot_wpdc_v1/phase1_wpdc_mobilenet_1-lrr_slim_aflw.pth.tar
    AFLW
    Test lines: 21080
    Extracting params take  5.618s
    [ 0, 30]        Mean: 4.316, Std: 3.823
    [30, 60]        Mean: 5.000, Std: 5.031
    [60, 90]        Mean: 5.659, Std: 6.825
    [ 0, 90]        Mean: 4.749, Std: 4.875
    [ 0, 90]        Mean: 4.991, Std: 0.548
    AFLW-2000
    Test lines: 2000
    Extracting params take  0.910s
    [ 0, 30]        Mean: 2.952, Std: 1.526
    [30, 60]        Mean: 3.706, Std: 2.335
    [60, 90]        Mean: 4.698, Std: 2.392
    [ 0, 90]        Mean: 3.363, Std: 1.962
    [ 0, 90]        Mean: 3.785, Std: 0.715


3D_Landmarks_68_ResNet-22
    AFLW
    100%|█████████████████████████████████████████████████████████████████████████████████████████| 21080/21080 [00:59<00:00, 356.01it/s]
    Extracting params take  59.233s
    [ 0, 30]        Mean: 3.983, Std: 4.013
    [30, 60]        Mean: 4.249, Std: 3.658
    [60, 90]        Mean: 4.953, Std: 6.219
    [ 0, 90]        Mean: 4.395, Std: 0.409

    AFLW2000
    100%|███████████████████████████████████████████████████████████████████████████████████████████| 2000/2000 [00:06<00:00, 330.21it/s]
    Extracting params take  6.060s
    [ 0, 30]        Mean: 2.710, Std: 1.722
    [30, 60]        Mean: 3.486, Std: 1.746
    [60, 90]        Mean: 4.813, Std: 4.656
    [ 0, 90]        Mean: 3.670, Std: 0.869


3D_Landmarks_68_ResNet-50_Deep.onnx # from 120x120
    AFLW
    100%|█████████████████████████████████████████████████████████████████████████████████████████| 21080/21080 [02:03<00:00, 170.65it/s]
    Extracting params take  123.543s
    [ 0, 30]        Mean: 3.722, Std: 3.624
    [30, 60]        Mean: 4.223, Std: 3.902
    [60, 90]        Mean: 5.271, Std: 6.943
    [ 0, 90]        Mean: 4.405, Std: 0.645

    AFLW2000
    100%|███████████████████████████████████████████████████████████████████████████████████████████| 2000/2000 [00:12<00:00, 153.90it/s]
    Extracting params take  12.998s
    [ 0, 30]        Mean: 2.807, Std: 1.692
    [30, 60]        Mean: 3.848, Std: 2.015
    [60, 90]        Mean: 5.149, Std: 3.528
    [ 0, 90]        Mean: 3.935, Std: 0.958
"""
