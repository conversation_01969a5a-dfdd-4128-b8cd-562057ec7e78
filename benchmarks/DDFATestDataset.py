

import os
import cv2
import torch
import numpy as np
import torch.utils.data as data

from pathlib import Path


class ToTensorGjz(object):
    def __call__(self, pic):
        if isinstance(pic, np.ndarray):
            img = torch.from_numpy(pic.transpose((2, 0, 1)))
            return img.float()

    def __repr__(self):
        return self.__class__.__name__ + '()'


class NormalizeGjz(object):
    def __init__(self, mean, std):
        self.mean = mean
        self.std = std

    def __call__(self, tensor):
        tensor.sub_(self.mean).div_(self.std)
        return tensor


class DDFATestDataset(data.Dataset):
    def __init__(self, filelists, root='', transform=None):
        self.root = root
        self.transform = transform
        self.lines = Path(filelists).read_text().strip().split('\n')

    def __len__(self):
        return len(self.lines)

    def __getitem__(self, index):
        path = os.path.join(self.root, self.lines[index])
        img = cv2.imread(path, cv2.IMREAD_COLOR)

        if self.transform is not None:
            img = self.transform(img)
        return img, path
