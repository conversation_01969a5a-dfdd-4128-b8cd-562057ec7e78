""" MobileNets-V1

Paper Description:
    Date: [Submitted on 17 Apr 2017]
    Link: https://arxiv.org/abs/1704.04861
    Title: MobileNets: Efficient Convolutional Neural Networks for Mobile Vision Applications
"""
import math
import torch.nn as nn


__all__ = ['MobileNet',
           'mobilenet',
           'mobilenet_2',
           'mobilenet_1',
           'mobilenet_075',
           'mobilenet_05',
           'mobilenet_025']


class DepthWiseBlock(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1, prelu=False):
        super(DepthWiseBlock, self).__init__()

        in_channels, out_channels = int(in_channels), int(out_channels)
        
        self.conv_dw = nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, stride=stride, groups=in_channels, bias=False)
        self.bn1 = nn.BatchNorm2d(in_channels)

        self.conv_pw = nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=1, padding=0, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)
        
        if prelu:
            self.relu = nn.PReLU()
        else:
            self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        x = self.conv_dw(x)
        x = self.bn1(x)
        x = self.relu(x)

        x = self.conv_pw(x)
        x = self.bn2(x)
        x = self.relu(x)

        return x


class MobileNet(nn.Module):
    def __init__(self, num_classes=1000, input_channel=3, scale=1.0, prelu=False):
        """ MobileNet-V1 with Depthwise Separable Convolution

        Args:
            num_classes: number of classes
            input_channel: number of image channels
            scale: controls the width of the network
            prelu: apply Parametric Rectified Linear Unit
        """
        super(MobileNet, self).__init__()

        self.conv_stem = nn.Conv2d(input_channel, int(32 * scale), kernel_size=3, stride=2, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(int(32 * scale))

        if prelu:
            self.relu = nn.PReLU()
        else:
            self.relu = nn.ReLU(inplace=True)

        self.dw2_1 = DepthWiseBlock(32 * scale, 64 * scale, prelu=prelu)
        self.dw2_2 = DepthWiseBlock(64 * scale, 128 * scale, stride=2, prelu=prelu)

        self.dw3_1 = DepthWiseBlock(128 * scale, 128 * scale, prelu=prelu)
        self.dw3_2 = DepthWiseBlock(128 * scale, 256 * scale, stride=2, prelu=prelu)

        self.dw4_1 = DepthWiseBlock(256 * scale, 256 * scale, prelu=prelu)
        self.dw4_2 = DepthWiseBlock(256 * scale, 512 * scale, stride=2, prelu=prelu)

        self.dw5_1 = DepthWiseBlock(512 * scale, 512 * scale, prelu=prelu)
        self.dw5_2 = DepthWiseBlock(512 * scale, 512 * scale, prelu=prelu)
        self.dw5_3 = DepthWiseBlock(512 * scale, 512 * scale, prelu=prelu)
        self.dw5_4 = DepthWiseBlock(512 * scale, 512 * scale, prelu=prelu)
        self.dw5_5 = DepthWiseBlock(512 * scale, 512 * scale, prelu=prelu)
        self.dw5_6 = DepthWiseBlock(512 * scale, 1024 * scale, stride=2, prelu=prelu)

        self.dw6 = DepthWiseBlock(1024 * scale, 1024 * scale, prelu=prelu)

        self.avgpool = nn.AdaptiveAvgPool2d(1)
        self.classifier = nn.Linear(int(1024 * scale), num_classes)

        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

    def forward(self, x):
        x = self.conv_stem(x)
        x = self.bn1(x)
        x = self.relu(x)

        x = self.dw2_1(x)
        x = self.dw2_2(x)
        x = self.dw3_1(x)
        x = self.dw3_2(x)
        x = self.dw4_1(x)
        x = self.dw4_2(x)
        x = self.dw5_1(x)
        x = self.dw5_2(x)
        x = self.dw5_3(x)
        x = self.dw5_4(x)
        x = self.dw5_5(x)
        x = self.dw5_6(x)
        x = self.dw6(x)

        x = self.avgpool(x)
        x = x.view(x.size(0), -1)
        x = self.classifier(x)

        return x


def mobilenet(**kwargs):
    """
    Construct MobileNet.
    scale=2.0  for mobilenet_2
    scale=1.0  for mobilenet_1
    scale=0.75 for mobilenet_075
    scale=0.5  for mobilenet_05
    scale=0.25 for mobilenet_025
    """
    return MobileNet(
        num_classes=kwargs.get('num_classes', 1000),
        input_channel=kwargs.get('input_channel', 3),
        scale=kwargs.get('scale', 1.0),
        prelu=kwargs.get('prelu', False)
    )


def mobilenet_2(num_classes=1000, input_channel=3):
    return MobileNet(num_classes=num_classes, input_channel=input_channel, scale=2.0)


def mobilenet_1(num_classes=1000, input_channel=3):
    return MobileNet(num_classes=num_classes, input_channel=input_channel, scale=1.0)


def mobilenet_075(num_classes=1000, input_channel=3):
    return MobileNet(num_classes=num_classes, input_channel=input_channel, scale=0.75)


def mobilenet_05(num_classes=1000, input_channel=3):
    return MobileNet(num_classes=num_classes, input_channel=input_channel, scale=0.5)


def mobilenet_025(num_classes=1000, input_channel=3):
    return MobileNet(num_classes=num_classes, input_channel=input_channel, scale=0.25)
