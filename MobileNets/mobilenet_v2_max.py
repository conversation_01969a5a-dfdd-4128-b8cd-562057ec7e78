""" MobileNets-V2

Paper Description:
    Date: [Submitted on 13 Jan 2018 (v1), last revised 21 Mar 2019 (this version, v4)]
    Link: https://arxiv.org/abs/1801.04381
    Title: MobileNetV2: Inverted Residuals and Linear Bottlenecks
"""
import torch
import torch.nn as nn


__all__ = ['MobileNetV2',
           'mobilenetv2',
           'mobilenetv2_2',
           'mobilenetv2_1',
           'mobilenetv2_075',
           'mobilenetv2_05',
           'mobilenetv2_025']


def conv_bn_relu(in_channels, out_channels, stride):
    return nn.Sequential(
        nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False),
        nn.BatchNorm2d(out_channels),
        nn.ReLU6(inplace=True)
    )


class InvertedResidual(nn.Module):
    def __init__(self, in_channels, out_channels, stride, expand_ratio):
        super(InvertedResidual, self).__init__()
        self.stride = stride
        assert stride in [1, 2]

        hidden_dim = round(in_channels * expand_ratio)
        self.use_res_connect = (self.stride == 1) and (in_channels == out_channels)

        if expand_ratio == 1:
            self.conv = nn.Sequential(
                # dw
                nn.Conv2d(hidden_dim, hidden_dim, kernel_size=3, stride=stride, padding=1, groups=hidden_dim, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.ReLU6(inplace=True),
                # pw-linear
                nn.Conv2d(hidden_dim, out_channels, kernel_size=1, stride=1, padding=0, bias=False),
                nn.BatchNorm2d(out_channels),
            )
        else:
            self.conv = nn.Sequential(
                # pw
                nn.Conv2d(in_channels, hidden_dim, kernel_size=1, stride=1, padding=0, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.ReLU6(inplace=True),
                # dw
                nn.Conv2d(hidden_dim, hidden_dim, kernel_size=3, stride=stride, padding=1, groups=hidden_dim, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.ReLU6(inplace=True),
                # pw-linear
                nn.Conv2d(hidden_dim, out_channels, kernel_size=1, stride=1, padding=0, bias=False),
                nn.BatchNorm2d(out_channels),
            )

    def forward(self, x):
        if self.use_res_connect:
            return x + self.conv(x)
        else:
            return self.conv(x)


class MobileNetV2(nn.Module):
    def __init__(self, num_classes=1000, scale=1.0):
        """ MobileNet-V2 with InvertedResidual + Linear Bottlenecks

        Args:
            scale: controls the width of the network
            num_classes: number of classes
            input_channel: number of image channels
            prelu: apply Parametric Rectified Linear Unit
        """
        super(MobileNetV2, self).__init__()
        self.num_classes = num_classes

        def inv_res_block(in_channels, out_channels, stride, expand_ratio):
            return InvertedResidual(int(in_channels * scale), int(out_channels * scale), stride, expand_ratio)

        self.features = nn.Sequential(
            conv_bn_relu(3, int(32 * scale), 2),
            
            inv_res_block(32, 16, 1, 1),
            inv_res_block(16, 24, 2, 6),
            inv_res_block(24, 24, 1, 6),
            inv_res_block(24, 32, 2, 6),
            inv_res_block(32, 32, 1, 6),
            inv_res_block(32, 32, 1, 6),
            inv_res_block(32, 64, 2, 6),
            inv_res_block(64, 64, 1, 6),
            inv_res_block(64, 64, 1, 6),
            inv_res_block(64, 64, 1, 6),
            inv_res_block(64, 96, 1, 6),
            inv_res_block(96, 96, 1, 6),
            inv_res_block(96, 96, 1, 6),
            inv_res_block(96, 160, 2, 6),
            inv_res_block(160, 160, 1, 6),
            inv_res_block(160, 160, 1, 6),
            inv_res_block(160, 320, 1, 6),

            conv_bn_relu(int(320 * scale), int(1280 * scale), 1),
            nn.AdaptiveAvgPool2d(1)
        )
        self.classifier = nn.Linear(int(1280 * scale), num_classes)

    def forward(self, x):
        x = self.features(x)
        x = x.view(x.size(0), -1)
        x = self.classifier(x)
        return x


def mobilenetv2(**kwargs):
    """
    Construct MobileNetV2.
    scale=2.0  for mobilenetv2_2
    scale=1.0  for mobilenetv2_1
    scale=0.75 for mobilenetv2_075
    scale=0.5  for mobilenetv2_05
    scale=0.25 for mobilenetv2_025
    """
    return MobileNetV2(
        scale=kwargs.get('scale', 1.0),
        num_classes=kwargs.get('num_classes', 1000),
        input_channel=kwargs.get('input_channel', 3),
        prelu=kwargs.get('prelu', False)
    )


def mobilenetv2_2(num_classes=1000):
    return MobileNetV2(num_classes=num_classes, scale=2.0)


def mobilenetv2_1(num_classes=1000):
    return MobileNetV2(num_classes=num_classes, scale=1.0)


def mobilenetv2_075(num_classes=1000):
    return MobileNetV2(num_classes=num_classes, scale=0.75)


def mobilenetv2_05(num_classes=1000):
    return MobileNetV2(num_classes=num_classes, scale=0.5)


def mobilenetv2_025(num_classes=1000):
    return MobileNetV2(num_classes=num_classes, scale=0.25)
