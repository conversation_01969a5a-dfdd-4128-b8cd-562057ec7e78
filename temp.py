import torch
from icecream import ic

from architectures import mobilenet_v1
from architectures import mobilenet_v3

from MobileNets import mobilenet_v1_max
from MobileNets import mobilenet_v2_max
from MobileNets import mobilenet_v3_max
from MobileNets import mobilenet_v4_max

from timm import create_model, list_models


def show_models(model_name='ecaresnet50t', pretrained=True):
	models = list_models(f"{model_name}*", pretrained=pretrained)
	for i, model in enumerate(sorted(models)):
		print(i, model)


def load_timm_model(*args, **kwargs):
	model = create_model(model_name=kwargs["model_name"],
						 pretrained=True,
						 drop_rate=0.2,
						 drop_path_rate=0.2,
						 drop_block_rate=0.2,
						 num_classes=1000,
						 in_chans=3)
	
	kwargs["verbose"] = False
	if kwargs["verbose"]:
		for key, value in model.default_cfg.items():
			print(key, value)
	return model.cuda()

# show_models(model_name='mobilenetv2')
# model = create_model(model_name='mobilenetv2_100.ra_in1k').train()
# ic(model)

# model = mobilenet_v1.mobilenet_1()

dummy_input = torch.randn(1, 3, 128, 128)

# model = mobilenet_v1_max.mobilenet_05()(dummy_input)
# ic(model)
# model = mobilenet_v2_max.mobilenetv2_05()(dummy_input)
# ic(model)

model = mobilenet_v3_max.MobileNetV3(input_size=128, mode="large")#(dummy_input)
# ic(model)

# model = mobilenet_v4_max.mobilenetv4_hybrid_medium()# (dummy_input)
# torch.save(model)
# ic(model)
onnx_path = "MobileNetV3_large.onnx"
from utils import torch2onnx
torch2onnx.export_onnx_model(model, dummy_input, onnx_path, opset_version=11, simplify=True, verify=True)
