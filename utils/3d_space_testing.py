import cv2
import numpy as np

from icecream import ic
from utils.constants import SD, HD, FHD


# ====== TESTINGS =======
def create_3d_face(landmark_3d_68):
    # Get min max z coor value
    min_z = round(landmark_3d_68[:, 2].min())  # 166
    max_z = round(landmark_3d_68[:, 2].max())  # 452
    ic(min_z, max_z)

    ear = round(landmark_3d_68[15, 2])
    nose = round(landmark_3d_68[30, 2])
    ic(ear, nose)

    # Create a black background image
    image = np.zeros((SD[1], SD[0], 3), dtype=np.uint8)
    image[:] = (0, 0, 0)

    # Scale the coordinates to fit the image size (optional)
    scale_factor = 1  # Adjust as needed
    mean_x = np.mean(landmark_3d_68[:, 0])  # 441
    mean_y = np.mean(landmark_3d_68[:, 1])  # 187
    mean_z = np.mean(landmark_3d_68[:, 2])  # 36

    ic(mean_x, mean_y, mean_z)

    # Convert 3d into 2d landmarks
    facial_landmarks_2d = (
        (landmark_3d_68[:, :2] * scale_factor) + np.array(
            [(SD[0] / 2) - (mean_x * scale_factor),
            (SD[1] / 2) - (mean_y * scale_factor)])
    ).astype(int)

    # Draw facial landmarks
    for landmark in facial_landmarks_2d:
        cv2.circle(image, tuple(landmark), 3, (0, 255, 0), -1)
    cv2.circle(image, (600, 250), 5, (0, 0, 255), -1)

    return image


def calculate_rotation_angles(face_landmarks_3d):
    # Calculate the vector from the camera position to the target point
    camera_position = np.array([0, 0, 0], dtype=np.int32)
    target_point = np.array([455, 200, 55], dtype=np.int32)

    direction_vector = target_point - camera_position
    
    # # Calculate the vector from the center of the face to the target point
    face_center = np.mean(face_landmarks_3d, axis=0)
    direction_vector = target_point - face_center
    
    mul = 10
    # Calculate the yaw, pitch, and roll angles
    yaw = np.arctan2(direction_vector[0], direction_vector[2]) * 180 / np.pi
    pitch = np.arctan2(direction_vector[1], np.linalg.norm(direction_vector[[0, 2]])) * 180 / np.pi
    roll = 0  # For simplicity, assuming roll is 0
    
    # Calculate the roll angle based on the direction perpendicular to yaw and pitch
    # This assumes that the face is initially aligned with the x-axis
    perpendicular_direction = np.cross([0, 1, 0], direction_vector)
    roll = np.arctan2(perpendicular_direction[1], perpendicular_direction[0]) * 180 / np.pi
    # # roll -= 180
    return round(yaw*mul), round(pitch*mul), round(roll*mul)


def calculate_3d_pose(landmark_3d_68):
    yaw, pitch, roll = calculate_rotation_angles(landmark_3d_68)
    estimated_pose = f"yaw: {yaw},    pitch: {pitch},    roll: {roll}"
    ic(estimated_pose)
    estimated_pose = {'yaw': yaw, 'pitch': pitch, 'roll': roll}
    return estimated_pose


def show_3d(landmark_3d_68=None):
    import numpy as np
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D

    target_point = np.array([455, 200, 55], dtype=np.int32)

    # Generate some example 3D facial landmarks (68, 3)
    if landmark_3d_68 is None:
        landmark_3d_68 = np.random.rand(68, 3)

    # Plot 3D facial landmarks
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')

    # Scatter plot of facial landmarks
    ax.scatter(
        landmark_3d_68[:, 0],
        landmark_3d_68[:, 1],
        landmark_3d_68[:, 2],
        c='r', marker='o')

    # mean_x = np.mean(landmark_3d_68[:, 0])  # 441
    # mean_y = np.mean(landmark_3d_68[:, 1])  # 187
    # mean_z = np.mean(landmark_3d_68[:, 2])  # 36
    # factor = 2

    # nose_x = landmark_3d_68[30, 0]  # 441
    # nose_y = landmark_3d_68[30, 1]  # 187
    # nose_z = landmark_3d_68[30, 2]  # 36
    # dif_x = mean_x - landmark_3d_68[30, 0]
    # dif_y = mean_y - landmark_3d_68[30, 1]
    # dif_z = mean_z - landmark_3d_68[30, 2]
    # ic(dif_x,dif_y,dif_z)

    # target_point = np.array([mean_x-dif_x*factor, mean_y-dif_y*factor, mean_z-dif_z*factor], dtype=np.int32)
    # print(target_point)

    ax.scatter(
        target_point[0],
        target_point[1],
        target_point[2],
        c='b', marker='o')
    
    # Calculate rotation angles
    yaw, pitch, roll = calculate_rotation_angles(landmark_3d_68, target_point)
    ic(pitch, yaw, roll)

    # Set default elevation and azimuth
    # ax.view_init(elev=125, azim=85)
    ax.view_init(elev=pitch, azim=yaw)

    # Set labels and title
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title('3D Facial Landmarks')

    # Show plot
    plt.show()
    plt.close()
