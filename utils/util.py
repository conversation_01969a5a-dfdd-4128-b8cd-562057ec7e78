import os
import cv2
import torch
import random
import pickle
import numpy as np

from icecream import ic
from numpy import cos, sin
from platform import system


# Params normalization configs
bfm = pickle.load(open("weights/pkl_references/bfm_noneck_v3.pkl", 'rb'))
param_mean_std = pickle.load(open("weights/pkl_references/param_mean_std_62d_120x120.pkl", 'rb'))
meanshape_lmk_3D_68 = pickle.load(open("weights/pkl_references/meanshape_68_deep.pkl", 'rb'))


def init_deterministic_seed(seed=0, deterministic_mode=True):
    """ Setup random seed """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.backends.cudnn.benchmark = not deterministic_mode
    torch.backends.cudnn.deterministic = deterministic_mode


def setup_multi_processes():
    """	Setup multi-processing environment variables """

    # Set multiprocess start method as `fork` to speed up the training
    if system() != 'Windows':
        torch.multiprocessing.set_start_method('fork', force=True)

    # Disable opencv multithreading to avoid system being overloaded (incompatible with PyTorch DataLoader)
    cv2.setNumThreads(0)

    # Setup OMP threads
    if 'OMP_NUM_THREADS' not in os.environ:
        os.environ['OMP_NUM_THREADS'] = '16'

    # Setup MKL threads
    if 'MKL_NUM_THREADS' not in os.environ:
        os.environ['MKL_NUM_THREADS'] = '16'

    if 'KMP_DUPLICATE_LIB_OK' not in os.environ:
        os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'


def draw_axis(img, estimated_pose, verification, tdx=None, tdy=None, size=100, verbose=False):
    """ Function to draw the axis of the yaw, pitch, roll for the head pose estimation.

    Args:
        img (ndarray): target image to be drawn on
        estimated_pose (dict[flaot]): yaw, pitch, and roll rotations
        verification (bool): check if the desired head pose is confirmed
        tdx (int, optional): shift on x axis
        tdy (int, optional): shift on y axis
        verbose (bool, optional): print the yaw, pitch, roll rotations
        
    Returns:
        image (ndarray):  image with head pose axis.
    """
    # Extract yaw, pitch, roll
    yaw = estimated_pose["yaw"]
    pitch = estimated_pose["pitch"]
    roll = estimated_pose["roll"]

    if verbose:
        print(f"yaw:{yaw:.3}\t\tpitch={pitch:.3}\t\troll={roll:.3}")

    # Convert to radians
    yaw = np.deg2rad(yaw)
    pitch = np.deg2rad(pitch)
    roll = np.deg2rad(roll)
        
    # Shift axis by tdx and tdy coordinates
    if tdx != None and tdy != None:
        tdx = tdx
        tdy = tdy
    else:
        height, width = img.shape[:2]
        tdx = width / 2
        tdy = height / 2

    # X-Axis (pointing to right) | drawn in red
    x1 = size * (cos(yaw) * cos(roll)) + tdx
    y1 = size * (cos(pitch) * sin(roll) + cos(roll) * sin(pitch) * sin(yaw)) + tdy

    # Y-Axis (pointing to down) | drawn in green
    x2 = size * (-cos(yaw) * sin(roll)) + tdx
    y2 = size * (cos(pitch) * cos(roll) - sin(pitch) * sin(yaw) * sin(roll)) + tdy

    # Z-Axis (out of the screen) | drawn in blue
    x3 = size * (sin(yaw)) + tdx
    y3 = size * (-cos(yaw) * sin(pitch)) + tdy

    # Draw the head pose axis
    cv2.line(img, (int(tdx), int(tdy)), (int(x1), int(y1)), (255, 0, 0), 4)
    cv2.line(img, (int(tdx), int(tdy)), (int(x2), int(y2)), (0, 255, 0), 4)
    
    if verification:
        cv2.line(img, (int(tdx), int(tdy)), (int(x3), int(y3)), (0, 0, 255), 4)
    else:
        cv2.line(img, (int(tdx), int(tdy)), (int(x3), int(y3)), (255, 255, 0), 4)


def normalize_pose(estimated_pose, reference_pose=None):
    normalized_pose = {
        'yaw': estimated_pose['yaw'] - reference_pose['yaw'],
        'pitch': estimated_pose['pitch'] - reference_pose['pitch'],
        'roll': estimated_pose['roll'] - reference_pose['roll']
    }
    return normalized_pose["yaw"], normalized_pose["pitch"], normalized_pose["roll"]


def estimate_desired_pose(frame, estimated_pose, verbose=False):
    """ Set a limit to the desired head position """
    
    # Limit the view by custom rules
    yaw_range = (-15, 15)
    pitch_range = (-15, 15)
    roll_range = (-15, 15)
    reference_pose = {'yaw': 0, 'pitch': 0, 'roll': 0}

    # Normalized estimated pose to init starting point
    yaw, pitch, roll = normalize_pose(estimated_pose, reference_pose)

    # For testing
    if verbose:
        if yaw_range[0] <= yaw <= yaw_range[1]:
            print_bg_text(frame, f"yaw: {round(yaw)} - True", order=4.0)
        else:
            print_bg_text(frame, f"yaw: {round(yaw)} - False", order=4.0)

        if pitch_range[0] <= pitch <= pitch_range[1]:
            print_bg_text(frame, f"pitch: {round(pitch)} - True", order=4.65)
        else:
            print_bg_text(frame, f"pitch: {round(pitch)} - False", order=4.65)
            
        if roll_range[0] <= roll <= roll_range[1]:
            print_bg_text(frame, f"roll: {round(roll)} - True", order=5.3)
        else:
            print_bg_text(frame, f"roll: {round(roll)} - False", order=5.3)

    if (
        (yaw_range[0] <= yaw <= yaw_range[1]) and
        (pitch_range[0] <= pitch <= pitch_range[1]) and
        (roll_range[0] <= roll <= roll_range[1])
    ):
        return True
    else:
        return False


def print_bg_text(frame, text, order=0, fontFace=cv2.FONT_HERSHEY_SIMPLEX, fontScale=0.65, thickness=1):
    """ Print white text result with black background """
    x_offset = 50	# 550
    y_offset = 40	# 120
    exra_shift = 4
    colors = {
        "black": (0, 0, 0),
        "white": (255, 255, 255),
        "red": (0, 0, 255),
        "green": (0, 255, 0),
        "blue": (255, 0, 0),
    }

    color_text = colors["white"]
    color_field = colors["black"]

    line_space = int(y_offset * order)
    org = (x_offset, y_offset+line_space)
    text_width, text_height = cv2.getTextSize(text=text, fontFace=fontFace, fontScale=fontScale, thickness=1)[0]

    start_point = (org[0] - exra_shift, org[1] + exra_shift)
    end_point = (org[0] + exra_shift + text_width, org[1] - exra_shift - text_height)

    cv2.rectangle(frame, start_point, end_point, color_field, cv2.FILLED)
    cv2.putText(frame, text, org=org, fontFace=fontFace, fontScale=fontScale, color=color_text, thickness=thickness)
