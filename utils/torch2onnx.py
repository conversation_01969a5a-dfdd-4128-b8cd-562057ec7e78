import onnx
import torch

from onnxsim import simplify


def symplify_onnx_model(onnx_path):
	# Load your predefined ONNX model
	model = onnx.load(onnx_path)

	# Convert model
	model_simplified, check = simplify(model)
	assert check, "Simplified ONNX model could not be validated"

	# Save simplified model
	onnx.save(model_simplified, onnx_path)


def check_onnx_model(onnx_path, verbose=False):
	# Load the ONNX model
	model = onnx.load(onnx_path)

	# Check that the model is well formed
	onnx.checker.check_model(model, full_check=True)

	# Print a human readable representation of the graph
	if verbose: print(onnx.helper.printable_graph(model.graph))


def export_onnx_model(model, example_input, onnx_path, opset_version=11, simplify=True, verify=True):
	""" Save the model in ONNX format """
	export_params = True
	do_constant_folding = True
	input_names = ["input"]
	output_names = ["output"]
	dynamic_axes = None
	verbose = False

	if dynamic_axes:
		dynamic_axes = {
			input_names[0]: {0: 'batch_size'},
			output_names[0]: {0: 'batch_size'}
		}


	# Export the PyTorch model to ONNX
	torch.onnx.export(
		model=model,								# model being run
		args=example_input,							# model input (or a tuple for multiple inputs)
		f=onnx_path,								# where to save the model
		export_params=export_params,				# store the trained parameter weights inside the model
		opset_version=opset_version,			# the ONNX version to export the model to
		do_constant_folding=do_constant_folding,	# to execute constant folding for optimization
		input_names=input_names,					# specify the names of input
		output_names=output_names,					# specify the names of output
		verbose=verbose,							# prints a description of the model being exported to stdout
		dynamic_axes=dynamic_axes					# variable length axes
	)

	if simplify: symplify_onnx_model(onnx_path)
	if verify: check_onnx_model(onnx_path)


if __name__ =="__main__":
	onnx_path = "SCRFD.onnx"
	example_input =  (1, 3, 640, 640)

	checkpoint_path = "pth_to_onnx/model.pth"
	checkpoint = torch.load(checkpoint_path, map_location='cpu')

	if 'optimizer' in checkpoint:
		del checkpoint['optimizer']
		tmp_ckpt_file = checkpoint_path+"_slim.pth"
		torch.save(checkpoint, tmp_ckpt_file)
		print('remove optimizer params and save to', tmp_ckpt_file)
		checkpoint_path = tmp_ckpt_file

	export_onnx_model(checkpoint, example_input, onnx_path, opset_version=11, simplify=True, verify=True)
