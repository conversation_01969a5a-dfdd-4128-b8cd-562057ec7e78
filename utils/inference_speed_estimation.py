"""
Last update:    2024.06.01
Researcher:     MAKSYM CHERNOZHUKOV
Description:    Estimation Inference Speed with a dummy sample
Features:       Face Detection, 2D-3D Facial Landmarks
"""
import numpy as np

from tqdm import tqdm
from argparse import ArgumentParser
from time import perf_counter as timer

from utils import util
from nets.FaceDetector import Face<PERSON>etectorONN<PERSON>
from nets.Landmarks3DDetectorDeep import Landmarks3DDetectorONNX as Landmarks3DDetectorONNXDEEP
from nets.Landmarks3DDetector3DDFA import Landmarks3DDetectorONNX as Landmarks3DDetectorONNX3DDFA


util.setup_multi_processes()
util.init_deterministic_seed()


def run_inference_pipeline(args):
    # Load onnx models
    face_detector = FaceDetectorONNX(
        args.face_onnx,
        apply_all_optim=args.onnx_optimizers,
        device=args.device)

    landmarks_detector_DEEP = Landmarks3DDetectorONNXDEEP(
        args.landmarksDeep_onnx,
        apply_all_optim=args.onnx_optimizers,
        device=args.device).load_model()
    
    landmarks_detector_3DDFA = Landmarks3DDetectorONNX3DDFA(
        args.landmarks3DDFA_onnx,
        apply_all_optim=args.onnx_optimizers,
        device=args.device).load_model()
    
    total = 1_000
    bbox = np.array([200, 100, 270, 200])
    frame = np.random.randint(0, 256, (480, 640, 3), dtype=np.uint8)

    if args.version == "DEEP":
        landmarks_detector = landmarks_detector_DEEP 
    elif args.version == "DFA":
        landmarks_detector = landmarks_detector_3DDFA 

    if args.version == "face":
        # For Face Detector
        # ===============================================================================
        for _ in range(5):
            face_detector.detect(frame,
                                 input_size=(640, 640),
                                 score_thresh=args.score_threshold,
                                 nms_thresh=args.nms_threshold)
            
        t0 = timer()
        for _ in tqdm(range(total)):
            face_detector.detect(frame,
                                 input_size=(640, 640),
                                 score_thresh=args.score_threshold,
                                 nms_thresh=args.nms_threshold)
        t1 = timer()
        # ===============================================================================
    else:
        # For Facial Landmarks
        # ===============================================================================
        for _ in range(5):
            landmarks_detector.predict(frame, bbox)

        t0 = timer()
        for _ in tqdm(range(total)):
            landmarks_detector.predict(frame, bbox)
        t1 = timer()
        # ===============================================================================
    process_time = round(((t1 - t0) / total) * 1000, 1)
    print(f"{process_time} ms")


def main():
    parser = ArgumentParser()
    parser.add_argument('--version', default="DEEP", type=str, help='face, DEEP, DFA')

    parser.add_argument('--face-onnx', default="weights/SCRFD_2.5G.onnx", type=str)
    parser.add_argument('--landmarksDeep-onnx', default="weights/3D_Landmarks_68_ResNet-50_Deep.onnx", type=str)
    parser.add_argument('--landmarks3DDFA-onnx', default="weights/3D_Landmarks_68_ResNet-22.onnx", type=str)

    parser.add_argument('--nms_threshold', default=0.4, type=float, help='maximum nms threshold')
    parser.add_argument('--score_threshold', default=0.5, type=float, help='minimum conf score threshold')

    parser.add_argument('--device', default="cpu", type=str, help='cpu/cuda')
    parser.add_argument('--onnx-optimizers', default=True, type=bool, help='use all available ONNX optimizations')
    args = parser.parse_args()

    run_inference_pipeline(args)


if __name__ == '__main__':
    main()
