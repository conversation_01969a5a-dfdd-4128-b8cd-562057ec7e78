import io
import os
import sys
import copy
import onnx
import thop
import torch
import numpy as np
import onnxruntime as ort

from icecream import ic
from pathlib import Path
from onnxsim import simplify
from onnx2pytorch import ConvertModel
from ptflops import get_model_complexity_info


def symplify_onnx_model(onnx_path, output_path):
    # Load your predefined ONNX model
    model = onnx.load(onnx_path)

    # Convert model
    model_simplified, check = simplify(model)
    
    assert check, "Simplified ONNX model could not be validated"

    # Save simplified model
    onnx.save(model_simplified, output_path)


def check_onnx_model(onnx_path, verbose=False):
    # Load the ONNX model
    model = onnx.load(onnx_path)

    # Check that the model is well formed
    onnx.checker.check_model(model, full_check=True)

    # Print a human readable representation of the graph
    if verbose: print(onnx.helper.printable_graph(model.graph))


def rename_input_output(onnx_path):
    # Load the ONNX model
    onnx_model = onnx.load(onnx_path)

    # Get input and output
    for input_tensor in onnx_model.graph.input:
        input_name = input_tensor.name
        input_shape = [dim.dim_value if (dim.dim_value > 0 and dim.HasField('dim_value')) else 'None' for dim in input_tensor.type.tensor_type.shape.dim]
        print(f"Input Name: {input_name}, Input Shape: {input_shape}")
        if input_name != "input":
            input_tensor.name = "input"
            print(f"Input Name: {input_tensor.name}*")

    for output_tensor in onnx_model.graph.output:
        output_name = output_tensor.name
        print(f"Output Name: {output_name}")
        if output_name != "output":
            output_tensor.name = "output"
            print(f"Output Name: {output_tensor.name}*")

    # Save simplified model
    onnx.save(onnx_model, onnx_path)


def confirm_model_can_run(onnx_path):
    model = onnx.load(onnx_path)
    try:
        onnx.checker.check_model(model)
        print("The model is valid!")
    except Exception as e:
        print("Model validation error:", e)

    # Try running a dummy inference to see if the model works as expected
    sess = ort.InferenceSession(onnx_path)
    inputs = {inp.name: np.random.random(size=[dim if dim is not None else 1 for dim in inp.shape]).astype(np.float32) for inp in sess.get_inputs()}
    outputs = sess.run(None, inputs)


def convert_to_static(onnx_path, output_path):
    # Load onnx model
    model = onnx.load(onnx_path)

    # Define new shape
    new_shape = [1, 3, 640, 640]

    for input_tensor in model.graph.input:
        # Get input shape
        input_name = input_tensor.name
        input_shape = [dim.dim_value if dim.HasField('dim_value') else 'None' for dim in input_tensor.type.tensor_type.shape.dim]
        ic(input_name, input_shape)

        # Modify input shape
        for i, dim in enumerate(input_tensor.type.tensor_type.shape.dim):
            dim.dim_value = new_shape[i]

        # Check modified input shape
        modified_input_shape = [dim.dim_value if dim.HasField('dim_value') else 'dynamic' for dim in input_tensor.type.tensor_type.shape.dim]
        ic(input_name, modified_input_shape)

    # Save the modified ONNX model with static shapes
    onnx.save(model, output_path)

    # Set up an ONNX runtime session
    session = ort.InferenceSession(output_path)

    # Create dummy input data according to the new static input shape
    input_data = {input_tensor.name: np.random.randn(*input_tensor.shape).astype(np.float32) for input_tensor in session.get_inputs()}

    # Run inference
    outputs = session.run(None, input_data)
    print("Inference run successfully with static shapes:", outputs[0].shape)


def evaluate_onnx_model(onnx_path):
    # Load the ONNX model
    onnx_model = onnx.load(onnx_path)

    # Get input and output
    for input_tensor in onnx_model.graph.input:
        input_name = input_tensor.name
        input_shape = [dim.dim_value if (dim.dim_value > 0 and dim.HasField('dim_value')) else 'None' for dim in input_tensor.type.tensor_type.shape.dim]
        print(f"Input Name: {input_name}, Input Shape: {input_shape}")

    for output_tensor in onnx_model.graph.output:
        output_name = output_tensor.name
        print(f"Output Name: {output_name}")

    # Convert to PyTorch
    pytorch_model = ConvertModel(onnx_model)
    model_profile = copy.deepcopy(pytorch_model)
    model_profile.eval()

    # Evaluate PyTorch model
    input_size = input_shape[-1]
    # input_size = 640
    input_randn = torch.randn((1, 3, input_size, input_size))    
    flops, num_params = thop.profile(model_profile, inputs=[input_randn], verbose=False)
    flops, num_params = thop.clever_format(nums=[flops, num_params], format="%.3f")
    macs, params = get_model_complexity_info(model_profile, (3, input_size, input_size), print_per_layer_stat=False, flops_units="MMac")
    print(f'Number of FLOPs: {flops}')
    print(f'Number of parameters: {num_params}')
    print(f'Number of MACs: {macs}')
    # print(f'Number of parameters: {params}')


def main():
    root_path = "weights"
    onnx_files = sorted(os.listdir(root_path))

    output = "weights_modified"
    os.makedirs(output, exist_ok=True)

    for i, onnx_file in enumerate(onnx_files):
        # if "0.5" not in onnx_file:
        #     continue

        if "SCRFD" in onnx_file:
            continue

        if "onnx" not in onnx_file:
            continue

        # if "3D_Landmarks_68_MobileNetV1_1.0.onnx" != onnx_file:
        #     continue

        onnx_path = os.path.join(root_path, onnx_file)
        output_onnx_path = os.path.join(output, onnx_file)

        print("="*60)
        ic(output_onnx_path, i)

        # convert_to_static(onnx_path, output_onnx_path)

        # print("Step - 0")
        # # confirm_model_can_run(onnx_path)

        # print("Step - 1")
        # symplify_onnx_model(output_onnx_path, output_onnx_path)

        # print("Step - 2")
        # check_onnx_model(output_onnx_path)

        # print("Step - 3")
        # rename_input_output(output_onnx_path)

        # print("Step - 4\n")
        # evaluate_onnx_model(output_onnx_path)

        # print("Step - 0")
        # convert_to_static(onnx_path, output_onnx_path)

        # print("Step - 1")
        # symplify_onnx_model(onnx_path, output_onnx_path)

        # print("Step - 2")
        # check_onnx_model(output_onnx_path)

        # print(f"Step - 0: {onnx_path}\n")
        # evaluate_onnx_model(onnx_path)

        print("="*60)


if __name__ == "__main__":
    main()
